pipeline {
    agent any

    parameters {
        choice(
            name: 'DEPLOY_ENV',
            choices: ['dev', 'staging', 'prod'],
            description: '选择部署环境'
        )
        booleanParam(
            name: 'SKIP_TESTS',
            defaultValue: true,
            description: '跳过测试阶段'
        )
        booleanParam(
            name: 'CLEAN_BUILD',
            defaultValue: false,
            description: '清理构建缓存'
        )
    }

    tools {
        nodejs 'nodejs22'
    }

    environment {
        // 项目配置
        PROJECT_NAME = 'techproclub-web'
        BUILD_DIR = "dist/${PROJECT_NAME}"
        WEB_DIR = "./"
        
        // Node.js 配置
        NODE_OPTIONS = "--max-old-space-size=8192"
        
        // 部署配置 - 根据环境参数动态设置
        WORK_DIR = "techpro"
        TARGET_DIR = "/home/<USER>/ssd0/docker-data/${params.DEPLOY_ENV}/techpro"
        DOCKER_CONTAINER = "techpro"
        SSH_CONFIG = "${params.DEPLOY_ENV == 'prod' ? 'prod-server' : '152'}"
        
        // 飞书通知配置 - 使用凭据管理
        FEISHU_CHAT_ID = "oc_f33795af9b15faadf181445c47e054ed"
        FEISHU_TEMPLATE_ID = "AAqIZVKVEuIO8"
        
        // 构建信息
        BUILD_TIMESTAMP = "${new Date().format('yyyy-MM-dd HH:mm:ss', TimeZone.getTimeZone('Asia/Shanghai'))}"
    }

    stages {
        stage('Initialize') {
            steps {
                script {
                    // 设置构建显示名称
                    currentBuild.displayName = "#${BUILD_NUMBER} - ${params.DEPLOY_ENV}"
                    currentBuild.description = "Deploy to ${params.DEPLOY_ENV} environment"
                    
                    echo "=== 构建信息 ==="
                    echo "环境: ${params.DEPLOY_ENV}"
                    echo "构建时间: ${BUILD_TIMESTAMP}"
                    echo "跳过测试: ${params.SKIP_TESTS}"
                    echo "清理构建: ${params.CLEAN_BUILD}"
                    echo "==============="
                    
                    // 发送开始通知
                    sendFeishuNotification("构建开始", "TechProClub-前端开始构建 (${BUILD_TIMESTAMP})", "info")
                }
            }
        }

        stage('Checkout') {
            steps {
                echo '准备拉取代码'
                git branch: 'main', 
                    credentialsId: '0b979e87-71f6-42df-ae17-ae63e7726ada', 
                    url: 'http://ftb.htecshm.com:30000/web/techproclub.git'
                echo '代码拉取成功'
            }
        }

        stage('Install Dependencies') {
            steps {
                script {
                    dir("${WEB_DIR}") {
                        echo '验证 Node.js 环境'
                        sh 'node -v && npm -v'
                        
                        if (params.CLEAN_BUILD) {
                            echo '清理构建缓存'
                            sh 'rm -rf node_modules package-lock.json'
                        }
                        
                        def nodeModulesExists = fileExists 'node_modules'
                        if (!nodeModulesExists) {
                            echo '安装依赖'
                            sh """
                                export NODE_OPTIONS=${NODE_OPTIONS}
                                npm ci --prefer-offline --no-audit
                            """
                        } else {
                            echo '使用缓存的 node_modules，检查更新'
                            sh """
                                export NODE_OPTIONS=${NODE_OPTIONS}
                                npm ci --prefer-offline --no-audit
                            """
                        }
                    }
                }
            }
        }

        stage('Build') {
            steps {
                script {
                    dir("${WEB_DIR}") {
                        echo '开始构建项目'
                        sh """
                            export NODE_OPTIONS=${NODE_OPTIONS}
                            npm run build
                        """
                        
                        // 验证构建结果
                        if (!fileExists('build')) {
                            error '构建失败：build 目录不存在'
                        }
                        
                        // 创建构建目录并打包
                        sh "mkdir -p ../${BUILD_DIR}"
                        sh "tar -czvf ../${BUILD_DIR}/build.tar.gz build"
                        
                        echo '构建完成，文件已打包'
                    }
                }
            }
            post {
                success {
                    echo "前端构建成功"
                }
                failure {
                    error "前端构建失败，终止流程"
                }
            }
        }
        
        stage('Clean Remote') {
            steps {
                echo '清理远程目录'
                script {
                    sshPublisher(publishers: [
                        sshPublisherDesc(
                            configName: "${SSH_CONFIG}",
                            transfers: [
                                sshTransfer(
                                    execCommand: """
                                        echo "清理目录: ${TARGET_DIR}"
                                        rm -rf ${TARGET_DIR}/*
                                        echo "目录清理完成"
                                    """,
                                    execTimeout: 120000
                                )
                            ],
                            verbose: true
                        )
                    ])
                }
            }
        }

        stage('Deploy') {
            steps {
                echo '开始部署'
                script {
                    sshPublisher(publishers: [
                        sshPublisherDesc(
                            configName: "${SSH_CONFIG}",
                            transfers: [
                                sshTransfer(
                                    sourceFiles: "${BUILD_DIR}/**/*",
                                    removePrefix: "${BUILD_DIR}",
                                    remoteDirectory: "${WORK_DIR}",
                                    execCommand: """
                                        set -e
                                        
                                        echo "开始部署流程..."
                                        
                                        # 检查文件是否存在
                                        BUILD_FILE="${TARGET_DIR}/build.tar.gz"
                                        if [ ! -f "\$BUILD_FILE" ]; then
                                            echo "错误：build.tar.gz 文件不存在！"
                                            exit 1
                                        fi
                                        
                                        # 解压文件
                                        echo "解压前端文件..."
                                        tar -zxvf "\$BUILD_FILE" --strip-components=1 -C ${TARGET_DIR}
                                        
                                        # 清理压缩包
                                        rm -f "\$BUILD_FILE"
                                        
                                        # 重启服务
                                        echo "重启 Docker 服务..."
                                        if docker ps -q -f name=${DOCKER_CONTAINER}; then
                                            docker restart ${DOCKER_CONTAINER}
                                        else
                                            echo "警告：容器 ${DOCKER_CONTAINER} 不存在，尝试启动..."
                                            docker run -d --name ${DOCKER_CONTAINER} -p 80:80 -v ${TARGET_DIR}:/usr/share/nginx/html nginx:alpine || exit 1
                                        fi
                                        
                                        # 健康检查
                                        echo "等待服务启动..."
                                        sleep 5
                                        
                                        # 验证服务状态
                                        if docker ps | grep ${DOCKER_CONTAINER} | grep -q "Up"; then
                                            echo "服务已成功启动"
                                            
                                            # 简单的健康检查
                                            if command -v curl >/dev/null 2>&1; then
                                                echo "执行健康检查..."
                                                curl -f http://localhost/ >/dev/null 2>&1 && echo "健康检查通过" || echo "警告：健康检查失败"
                                            fi
                                        else
                                            echo "错误：服务启动失败"
                                            docker logs ${DOCKER_CONTAINER} || true
                                            exit 1
                                        fi
                                        
                                        echo "部署完成！"
                                    """,
                                    execTimeout: 300000
                                )
                            ],
                            verbose: true
                        )
                    ])
                }
            }
        }
    }

    post {
        always {
            script {
                // 清理工作区
                cleanWs(
                    cleanWhenNotBuilt: false,
                    deleteDirs: true,
                    disableDeferredWipeout: true,
                    patterns: [
                        [pattern: 'build/**', type: 'INCLUDE'],
                        [pattern: 'dist/**', type: 'INCLUDE'],
                        [pattern: 'node_modules/**', type: 'INCLUDE']
                    ]
                )
            }
        }
        success {
            script {
                echo "构建和部署成功！"
                def endTime = new Date().format("yyyy-MM-dd HH:mm:ss", TimeZone.getTimeZone("Asia/Shanghai"))
                sendFeishuNotification("构建成功", "TechProClub-前端构建成功完成 (${endTime})", "success")
            }
        }
        failure {
            script {
                echo "构建或部署失败，请检查日志。"
                def endTime = new Date().format("yyyy-MM-dd HH:mm:ss", TimeZone.getTimeZone("Asia/Shanghai"))
                sendFeishuNotification("构建失败", "TechProClub-前端构建失败 (${endTime})", "error")
            }
        }
        unstable {
            script {
                echo "构建不稳定"
                def endTime = new Date().format("yyyy-MM-dd HH:mm:ss", TimeZone.getTimeZone("Asia/Shanghai"))
                sendFeishuNotification("构建不稳定", "TechProClub-前端构建不稳定 (${endTime})", "warning")
            }
        }
    }
}

def sendFeishuNotification(String title, String content, String type = "info") {
    script {
        try {
            // 从 Jenkins 凭据管理获取敏感信息
            withCredentials([
                string(credentialsId: 'feishu-app-id', variable: 'FEISHU_APP_ID'),
                string(credentialsId: 'feishu-app-secret', variable: 'FEISHU_APP_SECRET')
            ]) {
                // 获取 token
                def token = getFeishuToken(FEISHU_APP_ID, FEISHU_APP_SECRET)
                if (token) {
                    sendFeishuMessage(token, title, content, type)
                    echo "飞书通知发送成功: ${title}"
                } else {
                    echo "获取飞书 token 失败，跳过通知"
                }
            }
        } catch (Exception e) {
            echo "发送飞书通知失败: ${e.getMessage()}"
        }
    }
}

def getFeishuToken(appId, appSecret) {
    try {
        def response = ''
        withEnv(["APP_ID=${appId}", "APP_SECRET=${appSecret}"]) {
            response = sh(
                script: '''
                    curl -s --max-time 30 \
                        --location --request POST "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal" \
                        --header "Content-Type: application/json" \
                        --data-raw "{\"app_id\":\"$APP_ID\",\"app_secret\":\"$APP_SECRET\"}"
                ''',
                returnStdout: true
            ).trim()
        }

        def json = readJSON text: response
        return json?.tenant_access_token ?: null

    } catch (Exception e) {
        echo "获取飞书 token 异常: ${e.getMessage()}"
        return null
    }
}

def sendFeishuMessage(String token, String title, String content, String type) {
    try {
        withEnv(["FEISHU_TOKEN=${token}", "MSG_TITLE=${title}", "MSG_CONTENT=${content}", "MSG_TYPE=${type}"]) {
            def payload = """
            {
                "msg_type": "text",
                "content": {
                    "text": "[\${MSG_TYPE}] \${MSG_TITLE} - \${MSG_CONTENT}"
                }
            }
            """

            sh(
                script: '''
                    curl -s --max-time 30 \
                        --location --request POST "https://open.feishu.cn/open-apis/message/v4/send/" \
                        --header "Content-Type: application/json" \
                        --header "Authorization: Bearer $FEISHU_TOKEN" \
                        --data-raw '"'"${payload}"'"'
                ''',
                returnStdout: true
            )
        }
    } catch (Exception e) {
        echo "发送飞书消息异常: ${e.getMessage()}"
    }
}
