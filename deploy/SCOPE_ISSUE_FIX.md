# Jenkins Pipeline 作用域问题解决方案

## 🔍 问题分析

您遇到的错误 `No such property: FEISHU_APP_ID for class: groovy.lang.Binding` 是典型的 Jenkins Pipeline 作用域问题。

### 问题原因

1. **全局函数作用域限制**：在 `pipeline` 块外定义的全局函数无法直接访问 `withCredentials` 块中的变量
2. **变量绑定问题**：`withCredentials` 创建的变量只在其块内可见
3. **Groovy 类加载器问题**：全局函数在不同的类加载器上下文中执行

## 🛠️ 解决方案

### 方案一：使用 env 前缀（推荐）

```groovy
def sendFeishuNotification(String title, String content, String type = "info") {
    try {
        withCredentials([
            string(credentialsId: 'feishu-app-id', variable: 'FEISHU_APP_ID'),
            string(credentialsId: 'feishu-app-secret', variable: 'FEISHU_APP_SECRET')
        ]) {
            // 使用 env 前缀访问凭据变量
            def token = getFeishuToken(env.FEISHU_APP_ID, env.FEISHU_APP_SECRET)
            if (token) {
                sendFeishuMessage(token, title, content, type)
                echo "飞书通知发送成功: ${title}"
            }
        }
    } catch (Exception e) {
        echo "发送飞书通知失败: ${e.getMessage()}"
    }
}
```

### 方案二：在 script 块内定义函数

```groovy
stage('Notify Start') {
    steps {
        script {
            // 在 script 块内定义函数
            def sendNotification = { title, content, type ->
                withCredentials([
                    string(credentialsId: 'feishu-app-id', variable: 'FEISHU_APP_ID'),
                    string(credentialsId: 'feishu-app-secret', variable: 'FEISHU_APP_SECRET')
                ]) {
                    // 直接访问变量，无需 env 前缀
                    def token = getFeishuToken(FEISHU_APP_ID, FEISHU_APP_SECRET)
                    // ... 其他逻辑
                }
            }
            
            sendNotification("构建开始", "开始构建...", "info")
        }
    }
}
```

### 方案三：传递凭据作为参数

```groovy
// 在需要通知的地方
withCredentials([
    string(credentialsId: 'feishu-app-id', variable: 'FEISHU_APP_ID'),
    string(credentialsId: 'feishu-app-secret', variable: 'FEISHU_APP_SECRET')
]) {
    sendFeishuNotification(env.FEISHU_APP_ID, env.FEISHU_APP_SECRET, "构建开始", "开始构建...", "info")
}

// 全局函数
def sendFeishuNotification(String appId, String appSecret, String title, String content, String type) {
    try {
        def token = getFeishuToken(appId, appSecret)
        if (token) {
            sendFeishuMessage(token, title, content, type)
        }
    } catch (Exception e) {
        echo "通知失败: ${e.getMessage()}"
    }
}
```

## 🎯 最佳实践

### 1. 简化的通知函数

```groovy
def notifyFeishu(String title, String content, String type = "info") {
    try {
        withCredentials([
            string(credentialsId: 'feishu-app-id', variable: 'APP_ID'),
            string(credentialsId: 'feishu-app-secret', variable: 'APP_SECRET')
        ]) {
            def response = sh(
                script: """
                    # 获取 token
                    TOKEN=\$(curl -s --max-time 30 \\
                        -X POST 'https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal' \\
                        -H 'Content-Type: application/json' \\
                        -d '{"app_id":"${env.APP_ID}","app_secret":"${env.APP_SECRET}"}' \\
                        | grep -o '"tenant_access_token":"[^"]*"' | cut -d'"' -f4)
                    
                    # 发送消息
                    if [ -n "\$TOKEN" ]; then
                        curl -s --max-time 30 \\
                            -X POST 'https://open.feishu.cn/open-apis/im/v1/messages?receive_id_type=chat_id' \\
                            -H "Authorization: Bearer \$TOKEN" \\
                            -H 'Content-Type: application/json' \\
                            -d '{
                                "msg_type": "text",
                                "receive_id": "${env.FEISHU_CHAT_ID}",
                                "content": {"text": "[${type}] ${title}: ${content}"}
                            }'
                        echo "通知发送成功"
                    else
                        echo "获取 token 失败"
                    fi
                """,
                returnStatus: true
            )
        }
    } catch (Exception e) {
        echo "通知异常: ${e.getMessage()}"
    }
}
```

### 2. 使用共享库（推荐用于大型项目）

创建 `vars/sendFeishuNotification.groovy`：

```groovy
def call(String title, String content, String type = "info") {
    withCredentials([
        string(credentialsId: 'feishu-app-id', variable: 'FEISHU_APP_ID'),
        string(credentialsId: 'feishu-app-secret', variable: 'FEISHU_APP_SECRET')
    ]) {
        // 在共享库中，可以直接访问凭据变量
        def token = getToken(FEISHU_APP_ID, FEISHU_APP_SECRET)
        sendMessage(token, title, content, type)
    }
}
```

## 🔧 修复后的完整示例

```groovy
pipeline {
    agent any
    
    environment {
        FEISHU_CHAT_ID = "oc_f33795af9b15faadf181445c47e054ed"
    }
    
    stages {
        stage('Notify Start') {
            steps {
                script {
                    notifyFeishu("构建开始", "TechProClub 前端开始构建", "info")
                }
            }
        }
        
        // ... 其他阶段
    }
    
    post {
        success {
            script {
                notifyFeishu("构建成功", "TechProClub 前端构建完成", "success")
            }
        }
        failure {
            script {
                notifyFeishu("构建失败", "TechProClub 前端构建失败", "error")
            }
        }
    }
}

// 简化的通知函数
def notifyFeishu(String title, String content, String type = "info") {
    try {
        withCredentials([
            string(credentialsId: 'feishu-app-id', variable: 'APP_ID'),
            string(credentialsId: 'feishu-app-secret', variable: 'APP_SECRET')
        ]) {
            sh """
                # 获取 token 并发送消息的一体化脚本
                TOKEN=\$(curl -s --max-time 30 \\
                    -X POST 'https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal' \\
                    -H 'Content-Type: application/json' \\
                    -d '{"app_id":"${env.APP_ID}","app_secret":"${env.APP_SECRET}"}' \\
                    | grep -o '"tenant_access_token":"[^"]*"' | cut -d'"' -f4)
                
                if [ -n "\$TOKEN" ]; then
                    curl -s --max-time 30 \\
                        -X POST 'https://open.feishu.cn/open-apis/im/v1/messages?receive_id_type=chat_id' \\
                        -H "Authorization: Bearer \$TOKEN" \\
                        -H 'Content-Type: application/json' \\
                        -d '{
                            "msg_type": "text",
                            "receive_id": "${env.FEISHU_CHAT_ID}",
                            "content": {"text": "[${type}] ${title}: ${content}"}
                        }'
                    echo "✅ 飞书通知发送成功: ${title}"
                else
                    echo "❌ 获取飞书 token 失败"
                fi
            """
        }
    } catch (Exception e) {
        echo "⚠️ 飞书通知异常: ${e.getMessage()}"
    }
}
```

## 📝 总结

1. **使用 `env.` 前缀**访问 `withCredentials` 中的变量
2. **简化函数设计**，减少复杂的嵌套调用
3. **使用一体化脚本**，在单个 shell 命令中完成所有操作
4. **添加适当的错误处理**和日志输出
5. **考虑使用共享库**来管理复杂的通用功能

这样可以有效避免作用域问题，同时保持代码的简洁性和可维护性。
