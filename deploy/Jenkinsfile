pipeline {
    agent any

    tools {
        nodejs 'nodejs18'  // 对应全局配置中的Node.js别名
    }

    environment {
        // 设置环境变量
        PROJECT_NAME = 'techproclub-web'
        NODE_HOME = "/opt/node/node-v22.17.0-linux-x64"
        NODE_BIN = "${NODE_HOME}/bin"
        BUILD_DIR = "dist/${PROJECT_NAME}"
        WORK_DIR = "techpro"
        TARGET_DIR = "/home/<USER>/ssd0/docker-data/dev/techpro"
        WEB_DIR = "./"
        // 添加 PATH 环境变量，确保 node 和 npm 命令可用
        PATH = "${NODE_BIN}:${env.PATH}"
        // 增加 Node.js 内存限制
        NODE_OPTIONS = "--max-old-space-size=8192"

        // 飞书通知配置
        FEISHU_APP_ID = "cli_a8f74508a51e900b"
        FEISHU_APP_SECRET = "odY9t0VnEhcH1jwtl1OOYgAzWfHs61EE"
//        FEISHU_CHAT_ID = "oc_17c2c0c06475326c887bf6c94681692b"
        FEISHU_CHAT_ID = "oc_f33795af9b15faadf181445c47e054ed"
        FEISHU_TEMPLATE_ID = "AAqIZVKVEuIO8"
    }

    stages {
        stage('Notify Start') {
            steps {
                script {
                    echo '发送构建开始通知'

                    // 获取飞书token的函数
                    def getFeishuToken = {
                        def response = sh(
                            script: """
                                curl -s --location --request POST 'https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal' \
                                --header 'Content-Type: application/json' \
                                --data-raw '{
                                    "app_id": "${FEISHU_APP_ID}",
                                    "app_secret": "${FEISHU_APP_SECRET}"
                                }' | grep -o '"tenant_access_token":"[^"]*"' | cut -d'"' -f4
                            """,
                            returnStdout: true
                        ).trim()

                        if (response && response.length() > 0) {
                            return response
                        } else {
                            echo "获取飞书token失败"
                            return null
                        }
                    }

                    // 发送飞书消息的函数
                    def sendFeishuMessage = { token, title, content ->
                        if (token) {
                            sh """
                                curl -s --location --request POST 'https://open.feishu.cn/open-apis/im/v1/messages?receive_id_type=chat_id' \
                                --header 'Authorization: Bearer ${token}' \
                                --header 'Content-Type: application/json' \
                                --data-raw '{
                                    "msg_type": "interactive",
                                    "receive_id": "${FEISHU_CHAT_ID}",
                                    "content": "{\\"type\\":\\"template\\",\\"data\\":{\\"template_id\\":\\"${FEISHU_TEMPLATE_ID}\\",\\"template_variable\\":{\\"title\\":\\"${title}\\",\\"content\\":\\"${content}\\"}}}"
                                }'
                            """
                        }
                    }

                    def token = getFeishuToken()
                    if (token) {
                        def startTime = new Date().format("yyyy-MM-dd HH:mm:ss", TimeZone.getTimeZone("Asia/Shanghai"))
                        sendFeishuMessage(token, "TechProClub", "TechProClub-前端开始构建 (${startTime})")
                        echo '构建开始通知发送成功'
                    } else {
                        echo '获取飞书token失败，跳过通知'
                    }
                }
            }
        }

        stage('Checkout') {
            steps {
                // 从 Git 仓库获取代码
                echo '准备拉取代码'
                git branch: 'main', credentialsId: '0b979e87-71f6-42df-ae17-ae63e7726ada', url: 'http://ftb.htecshm.com:30000/web/techproclub.git'
                echo '代码拉取成功'
            }
        }

        stage('Build') {
            steps {
                script {
                    // 正确进入前端项目目录
                    dir("${WEB_DIR}") {
                        
                         // 验证环境
                        sh 'node -v && npm -v'
                        
                        // 使用缓存提高构建速度
                        def nodeModulesExists = fileExists 'node_modules'
                        if (!nodeModulesExists) {
                            echo '安装依赖'
                            sh """
                                # export PATH=${NODE_BIN}:\$PATH
                                export NODE_OPTIONS=${NODE_OPTIONS}
                                npm install
                            """
                        } else {
                            echo '使用缓存的 node_modules'
                            sh """
                                # export PATH=${NODE_BIN}:\$PATH
                                export NODE_OPTIONS=${NODE_OPTIONS}
                                npm install --prefer-offline
                            """
                        }
                    
                        // 使用 npm 构建项目
                        echo '开始构建项目'
                        sh """
                            # export PATH=${NODE_BIN}:\$PATH
                            export NODE_OPTIONS=${NODE_OPTIONS}
                            npm run build
                        """
                        
                        // 确保构建目录存在
                        sh "mkdir -p ../${BUILD_DIR}"
                        
                        // 将生成的 build 文件夹打包压缩到构建目录
                        sh "tar -czvf ../${BUILD_DIR}/build.tar.gz build"
                    }
                }
            }
            post {
                success {
                    echo "前端构建成功"
                }
                failure {
                    error "前端构建失败，终止流程"
                }
            }
        }
        
        stage('Clean Remote') {
            steps {
                echo '清理远程目录'
                sshPublisher(publishers: [
                    sshPublisherDesc(
                        configName: '152',
                        transfers: [
                            sshTransfer(
                                execCommand: "rm -rf ${TARGET_DIR}/*",
                                execTimeout: 120000
                            )
                        ],
                        verbose: true
                    )
                ])
            }
        }

        stage('Deploy') {
            steps {
                echo '开始部署'
                script {
                    sshPublisher(publishers: [
                        sshPublisherDesc(
                            configName: '152',
                            transfers: [
                                sshTransfer(
                                    sourceFiles: "${BUILD_DIR}/**/*",
                                    removePrefix: "${BUILD_DIR}",
                                    remoteDirectory: "${WORK_DIR}",
                                    execCommand: """
                                        # 检查文件是否存在
                                        if [ ! -f "/home/<USER>/ssd0/docker-data/dev/techpro/build.tar.gz" ]; then
                                            echo "错误：build.tar.gz 文件不存在！"
                                            exit 1
                                        fi
                                        
                                        # 解压build.tar.gz
                                        echo "解压前端文件..."
                                        tar -zxvf /home/<USER>/ssd0/docker-data/dev/techpro/build.tar.gz --strip-components=1 -C ${TARGET_DIR}
                                        
                                        # 删除build.tar.gz
                                        rm -rf /home/<USER>/ssd0/docker-data/dev/techpro/build.tar.gz
                                        
                                        # 重启docker服务
                                        echo "重启Nginx服务..."
                                        docker restart techpro || { echo "重启Nginx失败"; exit 1; }
                                        
                                        # 验证服务是否正常运行
                                        sleep 3
                                        if docker ps | grep techpro; then
                                            echo "Nginx服务已成功重启"
                                        else
                                            echo "Nginx服务重启失败"
                                            exit 1
                                        fi
                                    """,
                                    execTimeout: 180000
                                )
                            ],
                            verbose: true
                        )
                    ])
                }
            }
        }
    }

    post {
        always {
            // 清理工作区
            cleanWs(cleanWhenNotBuilt: false,
                    deleteDirs: true,
                    disableDeferredWipeout: true,
                    patterns: [[pattern: 'build/**', type: 'INCLUDE']])
        }
        success {
            script {
                echo "构建和部署成功！"

                // 获取飞书token的函数
                def getFeishuToken = {
                    def response = sh(
                        script: """
                            curl -s --location --request POST 'https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal' \
                            --header 'Content-Type: application/json' \
                            --data-raw '{
                                "app_id": "${FEISHU_APP_ID}",
                                "app_secret": "${FEISHU_APP_SECRET}"
                            }' | grep -o '"tenant_access_token":"[^"]*"' | cut -d'"' -f4
                        """,
                        returnStdout: true
                    ).trim()

                    if (response && response.length() > 0) {
                        return response
                    } else {
                        echo "获取飞书token失败"
                        return null
                    }
                }

                // 发送飞书消息的函数
                def sendFeishuMessage = { token, title, content ->
                    if (token) {
                        sh """
                            curl -s --location --request POST 'https://open.feishu.cn/open-apis/im/v1/messages?receive_id_type=chat_id' \
                            --header 'Authorization: Bearer ${token}' \
                            --header 'Content-Type: application/json' \
                            --data-raw '{
                                "msg_type": "interactive",
                                "receive_id": "${FEISHU_CHAT_ID}",
                                "content": "{\\"type\\":\\"template\\",\\"data\\":{\\"template_id\\":\\"${FEISHU_TEMPLATE_ID}\\",\\"template_variable\\":{\\"title\\":\\"${title}\\",\\"content\\":\\"${content}\\"}}}"
                            }'
                        """
                    }
                }

                def token = getFeishuToken()
                if (token) {
                    def endTime = new Date().format("yyyy-MM-dd HH:mm:ss", TimeZone.getTimeZone("Asia/Shanghai"))
                    sendFeishuMessage(token, "TechProClub", "TechProClub-前端构建成功完成 (${endTime})")
                    echo '构建成功通知发送成功'
                } else {
                    echo '获取飞书token失败，跳过通知'
                }
            }
        }
        failure {
            script {
                echo "构建或部署失败，请检查日志。"

                // 获取飞书token的函数
                def getFeishuToken = {
                    def response = sh(
                        script: """
                            curl -s --location --request POST 'https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal' \
                            --header 'Content-Type: application/json' \
                            --data-raw '{
                                "app_id": "${FEISHU_APP_ID}",
                                "app_secret": "${FEISHU_APP_SECRET}"
                            }' | grep -o '"tenant_access_token":"[^"]*"' | cut -d'"' -f4
                        """,
                        returnStdout: true
                    ).trim()

                    if (response && response.length() > 0) {
                        return response
                    } else {
                        echo "获取飞书token失败"
                        return null
                    }
                }

                // 发送飞书消息的函数
                def sendFeishuMessage = { token, title, content ->
                    if (token) {
                        sh """
                            curl -s --location --request POST 'https://open.feishu.cn/open-apis/im/v1/messages?receive_id_type=chat_id' \
                            --header 'Authorization: Bearer ${token}' \
                            --header 'Content-Type: application/json' \
                            --data-raw '{
                                "msg_type": "interactive",
                                "receive_id": "${FEISHU_CHAT_ID}",
                                "content": "{\\"type\\":\\"template\\",\\"data\\":{\\"template_id\\":\\"${FEISHU_TEMPLATE_ID}\\",\\"template_variable\\":{\\"title\\":\\"${title}\\",\\"content\\":\\"${content}\\"}}}"
                            }'
                        """
                    }
                }

                def token = getFeishuToken()
                if (token) {
                    def endTime = new Date().format("yyyy-MM-dd HH:mm:ss", TimeZone.getTimeZone("Asia/Shanghai"))
                    sendFeishuMessage(token, "TechProClub", "TechProClub-前端构建失败 (${endTime})")
                    echo '构建失败通知发送成功'
                } else {
                    echo '获取飞书token失败，跳过通知'
                }
            }
        }
    }
}