<template>
  <main class="page">
    <!-- banner -->
    <section id="banner">
      <img class="banner-image" src="/img/monitoring-1.png" />
      <div class="container">
        <span class="text_1">{{ t("monitoring.banner.text_1") }}</span>
      </div>
    </section>
    <!-- 介绍 -->
    <section id="intro" class="mt-100 container">
      <div class="flex-row items-center justify-center">
        <span class="text_1">{{ t("monitoring.intro.text_1") }}</span>
      </div>
      <div class="mt-50 swiper">
        <div class="swiper-btn" @click="swiper.prev()">
          <SvgIcon iconFileName="banner-left" width="1em" height="1em" />
        </div>
        <div class="swiper-content">
          <swiper-container ref="swiperRef">
            <swiper-slide v-for="(n, i) in 4">
              <img :src="`/img/monitoring-2_${n}.png`" />
            </swiper-slide>
          </swiper-container>
        </div>
        <div class="swiper-btn" @click="swiper.next()">
          <SvgIcon iconFileName="banner-right" width="1em" height="1em" />
        </div>
      </div>
      <div class="mt-100"></div>
    </section>
  </main>
</template>

<script setup name="about">
import { useRouter } from "vue-router";
import { reactive, onMounted } from "vue";
import { useSwiper } from "#imports";

const { t, locale, setLocale } = useI18n();
useHead({
  title: t("home.header_list.monitoring"),
  meta: [
    { name: "description", content: "我们提供专业的技术服务和创新解决方案" },
  ],
});

const swiperRef = ref(null);
const swiper = useSwiper(swiperRef, { loop: true });

onMounted(() => {});
</script>

<style scoped lang="less">
.page {
  display: flex;
  flex-direction: column;
  align-items: center;

  #intro {
    span {
      width: 1208px;
    }

    .text_1 {
      text-align: justify;
      font-size: 22px;
      line-height: 32px;
    }
  }
}
</style>
