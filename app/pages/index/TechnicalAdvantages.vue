<template>
  <!-- 技术优势 -->
  <div class="techBox type-area pb-md">
    <TitleBox
      :title="$t('home.technicalAdvantages_title')"
      :subTitle="$t('home.technicalAdvantages_subTitle')"
    />

    <div class="cardBox">
      <div class="cardItem" v-for="item in cardList" :key="item">
        <div class="cardItem-content">
          <div class="text-content">
            <div class="title-32">
              {{ $t(`home.technicalAdvantages_list.${item.title}`) }}
            </div>
            <div class="desc text-md">
              {{ $t(`home.technicalAdvantages_list.${item.subTitle}`) }}
            </div>
          </div>
          <div class="image-wrapper">
            <img :src="item.image" alt="" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="TechnicalAdvantages">
import img1 from "../../assets/images/核心优势-1.png";
import img2 from "../../assets/images/核心优势-2.png";
import img3 from "../../assets/images/核心优势-3.png";
import img4 from "../../assets/images/核心优势-4.png";

const cardList = [
  {
    title: "tit1",
    subTitle: "subTitle1",
    image: img1,
  },
  {
    title: "tit2",
    subTitle: "subTitle2",
    image: img2,
  },
  {
    title: "tit3",
    subTitle: "subTitle3",
    image: img3,
  },
  {
    title: "tit4",
    subTitle: "subTitle4",
    image: img4,
  },
];
</script>

<style lang="less" scoped>
.cardBox {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;

  .cardItem {
    width: calc(50% - 10px);
    height: 300px;
    background: linear-gradient(270deg, #f0f6ff 0%, #e1eaff 100%);
    box-shadow: 0px 6px 12px rgba(0, 0, 0, 0.04);

    transition: all 0.3s ease;
    cursor: pointer;
    overflow: hidden;

    .title-32 {
      color: #202124;
    }

    &:hover {
      background: linear-gradient(272.4deg, #1a73e8 2.01%, #1fb4ff 98.81%);
      box-shadow: -4px -2px 12px rgba(26, 115, 232, 0.32),
        4px 6px 12px rgba(26, 115, 232, 0.15);

      .title-32,
      .desc {
        color: #fff !important;
      }
    }

    &-content {
      height: 100%;
      padding: 40px;

      position: relative;

      padding-top: 46px;
    }

    .text-content {
      width: 75%;
      padding-right: 20px;

      .desc {
        margin-top: 16px;
        color: #666;
        line-height: 1.6;
      }
    }

    .image-wrapper {
      position: absolute;
      top: 6px;
      right: -54px;
      width: 250px;
      height: 250px;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
      }
    }
  }
}
</style>
