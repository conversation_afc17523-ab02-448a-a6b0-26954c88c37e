<template>
  <div class="partner-box type-area">
    <TitleBox
      :title="$t('home.partner_title')"
      :subTitle="$t('home.partner_subTitle')"
      className="pl-md"
    />
    <div class="partner-content">
      <swiper
        :modules="[Autoplay]"
        :slides-per-view="4"
        :space-between="30"
        :loop="true"
        :allow-touch-move="false"
        :autoplay="{
          delay: 1000,
          disableOnInteraction: false,
          reverseDirection: false,
        }"
        :speed="3000"
        class="partner-swiper mb-30"
      >
        <SwiperSlide v-for="(logo, index) in row1Logos" :key="logo.name">
          <div class="partner-item">
            <img :src="logo.url" :alt="logo.name" />
          </div>
        </SwiperSlide>
      </swiper>

      <swiper
        :modules="[Autoplay]"
        :slides-per-view="5"
        :space-between="30"
        :loop="true"
        :allow-touch-move="false"
        :autoplay="{
          delay: 1000,
          disableOnInteraction: false,
          reverseDirection: true,
          pauseOnMouseEnter: false,
        }"
        :speed="3000"
        class="partner-swiper mb-30"
      >
        <SwiperSlide v-for="(logo, index) in row2Logos" :key="logo.name">
          <div class="partner-item">
            <img :src="logo.url" :alt="logo.name" />
          </div>
        </SwiperSlide>
      </swiper>

      <swiper
        :modules="[Autoplay]"
        :slides-per-view="4"
        :space-between="30"
        :loop="true"
        :allow-touch-move="false"
        :autoplay="{
          delay: 1000,
          disableOnInteraction: false,
          reverseDirection: false,
          pauseOnMouseEnter: false,
        }"
        :speed="3000"
        class="partner-swiper"
      >
        <SwiperSlide v-for="(logo, index) in row3Logos" :key="logo.name">
          <div class="partner-item">
            <img :src="logo.url" :alt="logo.name" />
          </div>
        </SwiperSlide>
      </swiper>
    </div>
  </div>
</template>

<script setup name="Partner">
import { Swiper, SwiperSlide } from "swiper/vue";
import { Autoplay } from "swiper/modules";
import "swiper/css";
import "swiper/css/autoplay";

import HKPolyU from "../../assets/images/合作伙伴logo/香港理工大学.png";
import Stanford from "../../assets/images/合作伙伴logo/Stanford.png";
import Illinois from "../../assets/images/合作伙伴logo/illinois.png";
import Lehigh from "../../assets/images/合作伙伴logo/Lehigh.png";
import Tsinghua from "../../assets/images/合作伙伴logo/清华大学.png";
import Tongji from "../../assets/images/合作伙伴logo/同济大学.png";
import CSU from "../../assets/images/合作伙伴logo/中南大学.png";
import SZU from "../../assets/images/合作伙伴logo/深圳大学.png";
import TIML from "../../assets/images/合作伙伴logo/交通基建管理合约.png";
import HKPC from "../../assets/images/合作伙伴logo/生产力局i.png";
import SZCC from "../../assets/images/合作伙伴logo/特区建工.png";
import HKU from "../../assets/images/合作伙伴logo/香港大学.png";
import SEU from "../../assets/images/合作伙伴logo/东南大学.png";

const row1Logos = [
  { name: "香港理工大学", url: HKPolyU },
  { name: "Stanford", url: Stanford },
  { name: "Illinois", url: Illinois },
  { name: "Lehigh", url: Lehigh },
  { name: "香港理工大学", url: HKPolyU },
  { name: "Stanford", url: Stanford },
  { name: "Illinois", url: Illinois },
  { name: "Lehigh", url: Lehigh },
];

const row2Logos = [
  { name: "清华大学", url: Tsinghua },
  { name: "同济大学", url: Tongji },
  { name: "中南大学", url: CSU },
  { name: "深圳大学", url: SZU },
  { name: "交通基建管理合约", url: TIML },
  { name: "清华大学", url: Tsinghua },
  { name: "同济大学", url: Tongji },
  { name: "中南大学", url: CSU },
  { name: "深圳大学", url: SZU },
];

const row3Logos = [
  { name: "生产力局", url: HKPC },
  { name: "特区建工", url: SZCC },
  { name: "香港大学", url: HKU },
  { name: "东南大学", url: SEU },
  { name: "生产力局", url: HKPC },
  { name: "特区建工", url: SZCC },
  { name: "香港大学", url: HKU },
  { name: "东南大学", url: SEU },
];
</script>

<style lang="less" scoped>
.partner-box {
  padding-bottom: 100px;
}
.partner-content {
  position: relative;

  &::before,
  &::after {
    content: "";
    position: absolute;
    top: 0;
    width: 200px;
    height: 100%;
    z-index: 2;
    pointer-events: none;
  }

  &::before {
    left: 0;
    background: linear-gradient(to right, #fff 0%, rgba(255, 255, 255, 0) 100%);
  }

  &::after {
    right: 0;
    background: linear-gradient(to left, #fff 0%, rgba(255, 255, 255, 0) 100%);
  }

  .partner-swiper {
    width: 100%;
  }

  .mb-30 {
    margin-bottom: 30px;
  }

  .partner-item {
    // height: 180px;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;

    img {
      max-width: 80%;
      max-height: 80%;
      object-fit: contain;
    }
  }
}
</style>
