<template>
  <!-- 技术创新 -->
  <div class="techInnovation pb-md">
    <TitleBox
      class="type-area"
      :title="$t('home.techInnovation_title')"
      :subTitle="$t('home.techInnovation_subTitle')"
    />
    <div class="tech-content">
      <div class="type-area tech-list">
        <div
          class="tech-item"
          v-for="item in techList"
          :key="item.title"
          :class="`img-${item.id}`"
        >
          <div class="tech-item-mask">
            <SvgIcon
              :iconFileName="`icon_${item.icon}`"
              v-if="item.icon"
              width="80px"
              height="80px"
            />
            <div class="tech-item-title">
              {{ $t(`home.techInnovation_list.tit${item.id}`) }}
            </div>
            <div class="tech-item-subTitle">
              {{ $t(`home.techInnovation_list.subTitle${item.id}`) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="TechInnovation">
const techList = [
  {
    id: 1,
    icon: "机器人",
  },
  {
    id: 2,
    icon: "AI",
  },
  {
    id: 3,
    icon: "结构分析",
  },
  {
    id: 4,
    icon: "健康监测",
  },
];
</script>

<style lang="less" scoped>
.tech-content {
  height: 500px;
  background-image: url("../../assets/images/技术创新_bg.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.tech-list {
  display: flex;
  flex-wrap: wrap;
  height: 100%;
}
.tech-item {
  position: relative;
  flex: 1;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
.tech-item-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  color: #fff;

  background-color: rgba(60, 115, 222, 0.8);
  transition: all 0.3s ease-in-out;
  cursor: pointer;

  padding: 60px;
  text-align: center;
  padding-top: 50%;

  .svg-icon {
    transition: all 0.3s ease-in-out;
    margin-bottom: 15px;
  }

  .tech-item-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 10px;
    transition: all 0.3s ease-in-out;
  }

  .tech-item-subTitle {
    font-size: 22px;
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.3s ease-in-out;
  }

  &:hover {
    background-color: rgba(0, 0, 0, 0.8);

    .svg-icon {
      opacity: 0;
      transform: translateY(-20px);
      margin-bottom: 0;
    }

    .tech-item-title {
      transform: translateY(-60px);
    }

    .tech-item-subTitle {
      opacity: 1;
      transform: translateY(-50px);
    }
  }
}
.img-1 {
  background-image: url("../../assets/images/技术创新_机器人.png");
}
.img-2 {
  background-image: url("../../assets/images/技术创新_AI算法.png");
}
.img-3 {
  background-image: url("../../assets/images/技术创新_结构分析.png");
}
.img-4 {
  background-image: url("../../assets/images/技术创新_健康监测.png");
}
</style>
