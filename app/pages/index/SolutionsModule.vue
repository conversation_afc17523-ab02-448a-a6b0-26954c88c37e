<template>
  <!-- 解决方案模块 -->
  <div class="SolutionsModule type-area pb-md">
    <TitleBox
      :title="$t('home.solutions_title')"
      :subTitle="$t('home.solutions_subTitle')"
      className="pl-md"
    />
    <div class="swiper-container">
      <Swiper
        :modules="modules"
        :slides-per-view="3"
        :slides-per-group="1"
        :loop="true"
        :navigation="{
          nextEl: '.solutions-button-next',
          prevEl: '.solutions-button-prev',
        }"
        :space-between="40"
        :grid="{
          rows: 1,
        }"
      >
        <SwiperSlide v-for="(slide, index) in slides" :key="index">
          <div class="slide-item" @click="onSlideClick(slide)">
            <div class="slide-content">
              <img class="slide-img" :src="slide.img" />
              <span class="slide-title">{{
                $t(`home.solutions_list.${slide.title}`)
              }}</span>
            </div>
          </div>
        </SwiperSlide>
      </Swiper>

      <!-- <div class="swiper-button-prev custom-nav-btn solutions-button-prev">
        <SvgIcon iconFileName="banner-left" width="1em" height="1em" />
      </div>
      <div class="swiper-button-next custom-nav-btn solutions-button-next">
        <SvgIcon iconFileName="banner-right" width="1em" height="1em" />
      </div> -->
    </div>
  </div>
</template>

<script setup name="SolutionsModule">
import { ref } from "vue";
import { Swiper, SwiperSlide } from "swiper/vue";
import { Navigation, Pagination } from "swiper/modules";
import { goTo } from "../../utils/goTo";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import img1 from "../../assets/images/解决方案-1.png";
import img2 from "../../assets/images/解决方案-2.png";
import img3 from "../../assets/images/解决方案-3.png";

const modules = [Navigation, Pagination];
const { locale } = useI18n();

const slides = ref([
  { title: "tit1", img: img1, path: "/solutions" },
  { title: "tit2", img: img2, path: "/solutions" },
  { title: "tit3", img: img3, path: "/solutions" },
  { title: "tit2", img: img2, path: "/solutions" },
]);

const onSlideClick = async (slide) => {
  await goTo(locale.value, slide.path);
};
</script>

<style lang="less" scoped>
.swiper-container {
  position: relative;
}

.slide-item {
  padding: 5px;
  user-select: none;
  font-size: 24px;
  color: #fff;
  cursor: pointer;
  height: 620px;
  padding: 10px;
}

.slide-content {
  position: relative;
  width: 100%;
  height: 95%;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    box-shadow 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0 0 rgba(26, 115, 232, 0);

  &::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 0;
    background: linear-gradient(
      to top,
      rgba(26, 115, 232, 0.8) 0%,
      rgba(26, 115, 232, 0) 100%
    );
    transition: height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
  }

  &:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px -10px rgba(26, 115, 232, 0.25);
    &::after {
      height: 100%;
      opacity: 1;
    }
  }

  .slide-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .slide-title {
    position: absolute;
    left: 50%;
    bottom: 30px;
    width: 100%;
    transform: translateX(-50%);
    text-align: center;
    font-size: 36px;
    z-index: 1;
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

.custom-nav-btn {
  position: absolute;
  width: 48px;
  height: 48px;
  cursor: pointer;
  color: #a8b0bb;
  font-size: 52px;
  border-radius: 50%;
  &:hover {
    color: rgba(26, 115, 232, 1);
    background-color: rgba(26, 115, 232, 0.2);
    border-radius: 50%;
  }

  &.swiper-button-prev {
    left: -70px;
  }
  &.swiper-button-next {
    right: -70px;
  }
}
:deep(.swiper-button-prev),
:deep(.swiper-button-next) {
  &::after {
    display: none !important;
  }
}
</style>
