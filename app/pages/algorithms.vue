<template>
  <main class="page">
    <!-- banner -->
    <section id="banner">
      <img class="banner-image" src="/img/algorithms-1.png" />
      <div class="container">
        <span class="text_1">{{ t("algorithms.banner.text_1") }}</span>
      </div>
    </section>
    <!-- 介绍 -->
    <section id="intro" class="mt-80 container">
      <div class="swiper">
        <div class="swiper-btn" @click="swiper.prev()">
          <SvgIcon iconFileName="banner-left" width="1em" height="1em" />
        </div>
        <div class="swiper-content">
          <swiper-container ref="swiperRef">
            <swiper-slide>
              <img src="/img/algorithms-2.png" />
            </swiper-slide>
            <swiper-slide>
              <img src="/img/algorithms-3.png" />
            </swiper-slide>
          </swiper-container>
        </div>
        <div class="swiper-btn" @click="swiper.next()">
          <SvgIcon iconFileName="banner-right" width="1em" height="1em" />
        </div>
      </div>
      <div class="mt-80 flex-col items-center justify-center">
        <div class="section">
          <div class="ml-10 title">{{ t("algorithms.intro.title[0]") }}</div>
          <div class="ml-10 mt-27 subtitle">
            {{ t("algorithms.intro.subtitle[0]") }}
          </div>
          <div class="mt-30">
            <img src="/img/algorithms-4.png" />
          </div>
        </div>
      </div>
      <div class="mt-80 flex-col items-center justify-center">
        <div class="section">
          <div class="ml-10 title">{{ t("algorithms.intro.title[1]") }}</div>
          <div class="ml-10 mt-27 subtitle">
            {{ t("algorithms.intro.subtitle[1]") }}
          </div>
          <div class="mt-30">
            <img src="/img/algorithms-5.png" />
          </div>
        </div>
      </div>
    </section>
    <div class="mt-100"></div>
  </main>
</template>

<script setup name="about">
import { useRouter } from "vue-router";
import { reactive, onMounted } from "vue";
import { useSwiper } from "#imports";

const { t, locale, setLocale } = useI18n();
useHead({
  title: t("home.header_list.algorithms"),
  meta: [
    { name: "description", content: "我们提供专业的技术服务和创新解决方案" },
  ],
});

const swiperRef = ref(null);
const swiper = useSwiper(swiperRef, { loop: true });

onMounted(() => {});
</script>

<style scoped lang="less">
.page {
  display: flex;
  flex-direction: column;
  align-items: center;

  #intro {
    .section {
      width: 1208px;

      img {
        width: 100%;
      }
    }

    .text_1 {
      text-align: justify;
      font-size: 22px;
      line-height: 32px;
    }
  }
}
</style>
