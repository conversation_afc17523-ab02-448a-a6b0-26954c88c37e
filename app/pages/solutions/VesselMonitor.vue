<template>
  <!-- 漂浮船体监测平台 -->
  <div class="vessel-monitor text-page">
    <div class="container type-area">
      <div class="text-config-title">
        {{ $t("solutions.solutions_title5") }}
      </div>
      <div class="text-content-wrapper">
        <!-- 文字 -->
        <div class="textpage-content text-config">
          <div class="text-paragraph">
            {{ $t("solutions.vesselMonitor_content") }}
          </div>
        </div>
        <!-- 轮播 -->
        <div class="video-content">
          <CustomSwiper :slides="slides" custom-class="vessel-monitor" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="VesselMonitor">
import { ref } from "vue";
import img1 from "../../assets/images/解决方案/img1-漂浮船體監測平台.png";
import img2 from "../../assets/images/解决方案/img2-漂浮船體監測平台.png";

const slides = ref([
  {
    type: "image",
    src: img1,
    alt: "漂浮船體監測平台-1",
  },
  {
    type: "image",
    src: img2,
    alt: "漂浮船體監測平台-2",
  },
]);
</script>

<style lang="less" scoped>
.vessel-monitor {
  background: linear-gradient(180deg, #f2f6ff 0%, #ffffff 100%);

  .textpage-content {
    width: 60%;
    padding-right: 20px;
  }

  .video-content {
    width: 40%;
  }
}
</style>
