<template>
  <!-- 智能三維測量儀 -->
  <div class="TDMeasurement text-page">
    <div class="container type-area">
      <div class="text-config-title">
        {{ $t("solutions.solutions_title7") }}
      </div>
      <div class="text-content-wrapper">
        <!-- 文字 -->
        <div class="textpage-content text-config">
          <div class="text-paragraph">
            {{ $t("solutions.tdMeasurement_content1") }}
          </div>
          <div class="text-paragraph">
            {{ $t("solutions.tdMeasurement_content2") }}
            <ul class="text-list">
              <li>{{ $t("solutions.tdMeasurement_cnt01") }}</li>
              <li>{{ $t("solutions.tdMeasurement_cnt02") }}</li>
              <li>{{ $t("solutions.tdMeasurement_cnt03") }}</li>
              <li>{{ $t("solutions.tdMeasurement_cnt04") }}</li>
            </ul>
          </div>
        </div>
        <!-- 轮播 -->
        <div class="video-content">
          <CustomSwiper :slides="slides" custom-class="TDMeasurement" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="TDMeasurement">
import { ref } from "vue";
import img1 from "../../assets/images/解决方案/img1-智能三維測量儀.png";

const slides = ref([
  {
    type: "image",
    src: img1,
    alt: "智能三維測量儀",
  },
]);
</script>

<style lang="less" scoped>
.TDMeasurement {
  background: linear-gradient(180deg, #f2f6ff 0%, #ffffff 100%);

  .textpage-content {
    width: 60%;
    padding-right: 20px;
  }

  .video-content {
    width: 40%;
  }
  .text-list {
    list-style: disc;
    padding-left: 20px;
  }
}
</style>
