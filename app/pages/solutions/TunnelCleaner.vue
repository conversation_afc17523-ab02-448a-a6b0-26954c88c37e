<template>
  <!-- 隧道智能清洗机器人 -->
  <div class="tunnel-cleaner text-page">
    <div class="container type-area">
      <div class="text-config-title">
        {{ $t("solutions.solutions_title1") }}
      </div>
      <div class="text-content-wrapper">
        <!-- 文字 -->
        <div class="textpage-content text-config">
          <div class="text-paragraph">
            {{ $t("solutions.tunnelCleaner_content") }}
          </div>
          <div class="text-paragraph">
            {{ $t("solutions.tunnelCleaner_content2") }}
          </div>
        </div>
        <!-- 轮播 -->
        <div class="video-content">
          <CustomSwiper :slides="slides" custom-class="tunnel-cleaner" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="TunnelCleaner">
import { ref } from "vue";

import cleanerVideo from "/videos/隧道智能清洗机器人_水印_x264.mp4";
import cleanerImg from "../../assets/images/解决方案/img2-隧道智能清洗機器人.png";
import cleanerImg2 from "../../assets/images/解决方案/img3-隧道智能清洗機器人.png";

const slides = ref([
  {
    type: "video",
    src: cleanerVideo,
    alt: "隧道清洗机器人视频演示",
  },
  {
    type: "image",
    src: cleanerImg,
    alt: "隧道清洗机器人-2",
  },
  {
    type: "image",
    src: cleanerImg2,
    alt: "隧道清洗机器人-3",
  },
]);
</script>

<style lang="less" scoped>
.tunnel-cleaner {
  background-image: url("../../assets/images/解决方案/解决方案banner.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;

  .textpage-content {
    width: 60%;
    padding-right: 20px;
  }

  .video-content {
    width: 40%;
  }
}
</style>
