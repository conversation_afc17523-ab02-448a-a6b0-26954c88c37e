<template>
  <!-- 自主無人機 -->
  <div class="dronesPage">
    <div class="firstImg">
      <div class="firstImg-text">{{ $t("drones.firstImg") }}</div>
    </div>
    <div class="drones-content">
      <div class="basicModel text-page type-area">
        <div class="text-content-wrapper">
          <div class="img-wrapper">
            <img :src="img1" alt="" />
          </div>

          <div class="textpage-content">
            <div class="text-config-title">
              {{ $t("drones.drones01Tit") }}
            </div>
            <div class="text-config">
              {{ $t("drones.drones01Content") }}
            </div>
          </div>
        </div>
      </div>

      <div class="basicModel text-page type-area">
        <div class="text-content-wrapper">
          <div class="textpage-content">
            <div class="text-config-title">
              {{ $t("drones.drones02Tit") }}
            </div>
            <div class="text-config">
              {{ $t("drones.drones02Content") }}
            </div>
          </div>

          <div class="img-wrapper">
            <img :src="img2" alt="" />
          </div>
        </div>
      </div>

      <div class="basicModel text-page type-area">
        <div class="text-content-wrapper">
          <div class="img-wrapper">
            <img :src="img3" alt="" />
          </div>

          <div class="textpage-content">
            <div class="text-config-title">
              {{ $t("drones.drones03Tit") }}
            </div>
            <div class="text-config">
              {{ $t("drones.drones03Content") }}
            </div>
          </div>
        </div>
      </div>

      <div class="basicModel text-page type-area">
        <div class="text-content-wrapper">
          <div class="textpage-content">
            <div class="text-config-title">
              {{ $t("drones.drones04Tit") }}
            </div>
            <div class="text-config">
              {{ $t("drones.drones04Content") }}
            </div>
          </div>

          <div class="img-wrapper">
            <img :src="img4" alt="" />
          </div>
        </div>
      </div>

      <div class="basicModel text-page type-area">
        <div class="text-content-wrapper">
          <div class="img-wrapper">
            <img :src="img5" alt="" />
          </div>

          <div class="textpage-content">
            <div class="text-config-title">
              {{ $t("drones.drones05Tit") }}
            </div>
            <div class="text-config">
              {{ $t("drones.drones05Content") }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="Drones">
const { t, locale, setLocale } = useI18n();

import img1 from "../assets/images/机器人无人机/img1-自主無人機.png";
import img2 from "../assets/images/机器人无人机/img2-自主無人機.png";
import img3 from "../assets/images/机器人无人机/img3-自主無人機.png";
import img4 from "../assets/images/机器人无人机/img4-自主無人機.png";
import img5 from "../assets/images/机器人无人机/img5-自主無人機.png";

useHead({
  title: t("dronesPage"),
  meta: [
    { name: "description", content: "我们提供专业的技术服务和创新解决方案" },
  ],
});
</script>

<style lang="less" scoped>
.dronesPage {
  background-color: #f8faff;
}
.firstImg {
  height: 480px;
  background-image: url("../assets/images/机器人无人机/自主无人机-banner.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  .firstImg-text {
    width: 700px;
    font-size: 22px;
    line-height: 2;
    color: #fff;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-5%, -50%);
  }
}

.drones-content {
  .textpage-content {
    width: 60%;
  }
  padding: 30px 0;
  .text-content-wrapper {
    justify-content: space-between;
  }

  .img-wrapper {
    img {
      border-radius: 16px;
      width: 556px !important;
      height: 334px !important;
      object-fit: cover;
    }
  }
  .text-config-title {
    margin-bottom: 30px;
  }
}
</style>
