<template>
  <main class="page">
    <!-- banner -->
    <section id="banner">
      <img class="banner-image" src="/img/equipment-1.png" />
      <div class="container">
        <span class="text_1">{{ $t('equipment.banner.text_1') }}</span>
      </div>
    </section>
    <!-- 产品系列 -->
    <section id="product" class="mt-100 flex-row container">
      <div class="mt-30 flex-col left">
        <div class="title">{{ $t('equipment.product.title') }}</div>
        <div class="mt-50 text_1">{{ $t('equipment.product.text_1.0') }}</div>
        <div class="mt-10 text_1">{{ $t('equipment.product.text_1.1') }}</div>
        <div class="mt-10 text_1">{{ $t('equipment.product.text_1.2') }}</div>
      </div>
      <div class="flex-col right">
        <img class="image_2" src="/img/equipment-3.png" />
      </div>
    </section>
    <div class="mt-100"></div>
  </main>
</template>

<script setup name="about">
import { useRouter } from 'vue-router'
import { reactive, onMounted } from 'vue'

const { t, locale, setLocale } = useI18n()
useHead({
  title: t("home.header_list.equipment"),
  meta: [
    { name: "description", content: "我们提供专业的技术服务和创新解决方案" }
  ]
})
</script>

<style scoped lang="less">
.page {
  display: flex;
  flex-direction: column;
  align-items: center;

  #product {
    .left {
      padding: 10px;
      width: 40%;
    }

    .right {
      padding: 10px;
      width: 60%;
    }

    .text_1 {
      text-align: justify;
      font-size: 20px;
      line-height: 2em;
    }
  }
}
</style>
