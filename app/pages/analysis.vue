<template>
  <main class="page">
    <!-- banner -->
    <section id="banner">
      <img class="banner-image" src="/img/analysis-1.png" />
      <div class="container">
        <span class="text_1">{{ t("analysis.banner.text_1") }}</span>
      </div>
    </section>
    <!-- 介绍 -->
    <section id="intro" class="mt-100 container">
      <div class="swiper">
        <div class="swiper-btn" @click="swiper.prev()">
          <SvgIcon iconFileName="banner-left" width="1em" height="1em" />
        </div>
        <div class="swiper-content">
          <swiper-container ref="swiperRef">
            <swiper-slide>
              <video
                controls
                poster="/img/analysis-2.png"
                autoplay
                loop
                muted
                playsinline
              >
                <source src="/img/analysis-3.mp4" type="video/mp4" />
                您的浏览器不支持HTML5视频播放
              </video>
            </swiper-slide>
            <swiper-slide>
              <img class="slide_img" src="/img/analysis-3_2.png" />
            </swiper-slide>
            <swiper-slide>
              <img class="slide_img" src="/img/analysis-3_3.png" />
            </swiper-slide>
          </swiper-container>
        </div>
        <div class="swiper-btn" @click="swiper.next()">
          <SvgIcon iconFileName="banner-right" width="1em" height="1em" />
        </div>
      </div>
    </section>
    <div class="mt-100"></div>
  </main>
</template>

<script setup name="about">
import { useRouter } from "vue-router";
import { reactive, onMounted } from "vue";
import { useSwiper } from "#imports";

const { t, locale, setLocale } = useI18n();
useHead({
  title: t("home.header_list.analysis"),
  meta: [
    { name: "description", content: "我们提供专业的技术服务和创新解决方案" },
  ],
});

const swiperRef = ref(null);
const swiper = useSwiper(swiperRef, { loop: true });

onMounted(() => {
  console.log(swiper);
});
</script>

<style scoped lang="less">
.page {
  display: flex;
  flex-direction: column;
  align-items: center;

  #intro {
    span {
      width: 1208px;
    }

    .text_1 {
      text-align: justify;
      font-size: 22px;
      line-height: 32px;
    }

    .slide_img {
      border: 1px solid #eee;
      border-radius: 16px;
    }
  }
}
</style>
