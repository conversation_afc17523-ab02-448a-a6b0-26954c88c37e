<template>
  <div class="AppFooter">
    <div class="footTop type-area">
      <img src="../assets/images/footer-logo.png" class="footer-logo" />
      <div class="footTop-content">
        <span class="footTop-content-title">{{
          $t("footerPage.followUs")
        }}</span>
        <SvgIcon iconFileName="icon_Linkedin" width="112px" height="36px" />
        <SvgIcon iconFileName="icon_Instagram" width="124px" height="36px" />
        <SvgIcon iconFileName="icon_YouTube" width="46px" height="36px" />
        <a-tooltip color="#fff">
          <template #title>
            <div class="wechat-qrcode">
              <img src="../assets/images/qrCode.jpg" />
            </div>
          </template>
          <SvgIcon iconFileName="icon_wechat" width="43px" height="36px" />
        </a-tooltip>

        <SvgIcon iconFileName="icon_小红书" width="58px" height="36px" />
      </div>
    </div>
    <div class="footBottom-content type-area">
      <!-- 关于我们 -->
      <div class="aboutBox cntBox">
        <div class="cnt-title">{{ $t("footerPage.aboutUs.title") }}</div>
        <div class="cnt-content">
          <p class="showGoo" @click="goTo(locale, '/about')">
            {{ $t("footerPage.aboutUs.items[0]") }}
          </p>
          <p class="showGoo" @click="goTo(locale, '/about')">
            {{ $t("footerPage.aboutUs.items[1]") }}
          </p>
        </div>
      </div>
      <!-- 我们的服务 -->
      <div class="serviceBox cntBox">
        <div class="cnt-title">{{ $t("footerPage.ourServices.title") }}</div>
        <div class="cnt-content">
          <p class="showGoo" @click="goTo(locale, '/robots')">
            {{ $t("footerPage.ourServices.items[0]") }}
          </p>
          <p class="showGoo" @click="goTo(locale, '/algorithms')">
            {{ $t("footerPage.ourServices.items[1]") }}
          </p>
          <p class="showGoo" @click="goTo(locale, '/analysis')">
            {{ $t("footerPage.ourServices.items[2]") }}
          </p>
          <p class="showGoo" @click="goTo(locale, '/monitoring')">
            {{ $t("footerPage.ourServices.items[3]") }}
          </p>
        </div>
      </div>
      <!-- 标杆案例 -->
      <div class="caseBox cntBox">
        <div class="cnt-title">{{ $t("footerPage.benchmarkCases.title") }}</div>
        <div class="cnt-content">
          <p class="showGoo" @click="goTo(locale, '/solutions')">
            {{ $t("footerPage.benchmarkCases.items[0]") }}
          </p>
          <p class="showGoo" @click="goTo(locale, '/solutions')">
            {{ $t("footerPage.benchmarkCases.items[1]") }}
          </p>
          <p class="showGoo" @click="goTo(locale, '/solutions')">
            {{ $t("footerPage.benchmarkCases.items[2]") }}
          </p>
          <p class="showGoo" @click="goTo(locale, '/solutions')">
            {{ $t("footerPage.benchmarkCases.items[3]") }}
          </p>
        </div>
      </div>
      <div class="line"></div>
      <!-- 联系我们 -->
      <div class="contactBox cntBox">
        <div class="cnt-title">{{ $t("footerPage.contactUs.title") }}</div>
        <div class="cnt-content">
          <p class="contact-item">
            <SvgIcon iconFileName="icon_emall" width="24px" height="24px" />
            <span><EMAIL></span>
          </p>
          <p class="contact-item">
            <SvgIcon iconFileName="icon_地址" width="24px" height="24px" />
            <span
              @click="
                openGoogleMapsByAddress('香港特别行政区观塘鸿图道1号3017室')
              "
              class="contact-item-address showGoo"
            >
              {{ $t("footerPage.contactUs.address") }}
            </span>
          </p>
        </div>
      </div>
    </div>
    <!-- 版权信息 -->
    <div class="copyrightBox">
      Copyright © 2025 TechPro International Limited. All rights reserved.
    </div>
  </div>
</template>

<script setup name="AppFooter">
import { goTo } from "../utils/goTo";
const { locale } = useI18n();
const url = ref(
  "https://map.baidu.com/search/%E9%B4%BB%E5%9C%96%E9%81%931%E8%99%9F/@12716095.990745908,2533396.956060599,19z?querytype=s&da_src=shareurl&wd=%E9%B4%BB%E5%9C%96%E9%81%931%E8%99%9F&c=340&src=0&pn=0&sug=0&l=13&b=(12648061,2552312;12709501,2581464)&from=webmap&biz_forward=%7B%22scaler%22:1,%22styles%22:%22pl%22%7D&device_ratio=1"
);
function openGoogleMapsByAddress(address) {
  window.open(url.value, "_blank");
}

onMounted(async () => {
  try {
    const googleUrl =
      "https://www.google.com/maps/search/?api=1&query=%E9%A6%99%E6%B8%AF%E7%89%B9%E5%88%AB%E8%A1%8C%E6%94%BF%E5%8C%BA%E8%A7%82%E5%A1%98%E9%B8%BF%E5%9B%BE%E9%81%931%E5%8F%B73017%E5%AE%A4";
    await fetch(
      "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d35115.90006019387!2d114.20874018408284!3d22.311894483589693!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x34040148fa17f347%3A0xc71232b210ddbbfc!2z6KeA5aGY6bS75ZyW6YGTMeiZn-m0u-WclumBkzHomZ8!5e0!3m2!1szh-TW!2shk!4v1754381212043!5m2!1szh-TW!2shk",
      { method: "GET" }
    );
    url.value = googleUrl;
  } catch (error) {}
});
</script>
<style lang="less" scoped>
.AppFooter {
  padding: 0 60px;

  .footTop {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    height: 117px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .footer-logo {
      width: 223px;
    }

    .footTop-content {
      display: flex;
      align-items: center;

      .svg-icon {
        cursor: pointer;
      }

      .footTop-content-title {
        margin-right: 12px;
        color: #cfd2d6;
        line-height: 31px;
      }

      :nth-child(3) {
        // LinkedIn
        margin-right: 10px;
      }

      :nth-child(4) {
        // Instagram
        margin-right: 22px;
      }

      :nth-child(5) {
        // YouTube
        margin-right: 22px;
      }

      :nth-child(6) {
        // WeChat
        margin-right: 22px;
      }
    }

    .footTop-content-title {
      font-size: 22px;
      height: 36px;
      line-height: 36px;
      color: #ffffff;
      cursor: pointer;
    }
  }

  .footBottom-content {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    justify-content: space-between;
    padding: 50px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    .cntBox {
      .cnt-title {
        font-size: 22px;
        margin-bottom: 20px;
      }

      .cnt-content {
        font-size: 22px;
        color: #cfd2d6;

        p {
          margin-top: 10px;
        }
      }
    }

    .contactBox {
      .contact-item {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }

    .line {
      height: 188px;
      border-left: 1px solid rgba(255, 255, 255, 0.1);
    }
  }
}

.showGoo {
  cursor: pointer;

  &:hover {
    color: #1a73e8;
    text-decoration: underline;
  }
}

.copyrightBox {
  text-align: center;
  color: #cfd2d6;
  font-size: 22px;
  padding: 20px 0;
}
</style>
