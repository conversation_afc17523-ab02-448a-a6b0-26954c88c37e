<template>
  <div class="custom-swiper">
    <div class="swiper-container">
      <div class="swiper-wrapper">
        <Swiper
          :modules="[Navigation, Pagination, Autoplay]"
          :slides-per-view="1"
          :loop="true"
          :navigation="{
            nextEl: `.${customClass}-button-next`,
            prevEl: `.${customClass}-button-prev`,
          }"
          :pagination="{
            el: `.${customClass}-pagination`,
            type: 'fraction',
          }"
          :autoplay="
            isAutoPlay
              ? false
              : {
                  delay: 3000,
                  disableOnInteraction: true,
                  reverseDirection: false,
                  pauseOnMouseEnter: true,
                }
          "
          @slideChange="handleSlideChange"
          @swiper="onSwiper"
        >
          <SwiperSlide v-for="(slide, index) in slides" :key="index">
            <div class="slide-item">
              <video
                v-if="slide.type === 'video'"
                :src="slide.src"
                :ref="(el) => (videoRefs[index] = el)"
                controls
                muted
                loop
                autoplay
                playsinline
                class="slide-video"
              ></video>
              <img v-else :src="slide.src" :alt="slide.alt" />
            </div>
          </SwiperSlide>
        </Swiper>
      </div>
    </div>

    <div class="swiper-controls" v-if="slides.length > 1">
      <div :class="['swiper-button-prev', `${customClass}-button-prev`]">
        <SvgIcon iconFileName="banner-left" width="48" height="48" />
      </div>
      <div :class="`${customClass}-pagination`"></div>
      <div :class="['swiper-button-next', `${customClass}-button-next`]">
        <SvgIcon iconFileName="banner-right" width="48" height="48" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick, computed } from "vue";
import { Swiper, SwiperSlide } from "swiper/vue";
import { Navigation, Pagination, Autoplay } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";

const props = defineProps({
  slides: {
    type: Array,
    required: true,
  },
  customClass: {
    type: String,
    required: true,
  },
});

const isAutoPlay = computed(() => {
  // 判断内容中有没有type是video的
  return (
    props.slides.some((slide) => slide.type === "video") ||
    props.slides.length <= 1
  );
});

const videoRefs = ref([]);
const swiperInstance = ref(null);
const onSwiper = (swiper) => {
  swiperInstance.value = swiper;
};
const handleSlideChange = (swiper) => {
  videoRefs.value.forEach((video) => {
    if (video) {
      video.pause();
    }
  });
  nextTick(() => {
    const currentIndex = swiper.realIndex;
    const currentSlide = props.slides[currentIndex];

    if (currentSlide && currentSlide.type === "video") {
      const currentVideo = videoRefs.value[currentIndex];
      if (currentVideo) {
        currentVideo.currentTime = 0;
        currentVideo.play().catch((error) => {
          // console.log("自动播放失败:", error);
        });
      }
    }
  });
};
</script>
<style lang="less" scoped>
.custom-swiper {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .swiper-container {
    position: relative;
    width: 610px;
    height: 343px;
    background: #fff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    .swiper-wrapper {
      height: 100%;

      :deep(.swiper) {
        height: 100%;
      }
    }

    .slide-item {
      width: 100%;
      height: 100%;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  .swiper-controls {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-top: 20px;
  }
}

:deep(.swiper-button-prev),
:deep(.swiper-button-next) {
  position: static !important;
  width: 32px;
  height: 32px;
  margin: 0;
  color: #666;
  background: transparent;
  border-radius: 50%;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  &:hover {
    color: rgba(26, 115, 232, 1);
    background-color: rgba(26, 115, 232, 0.1);
  }

  &::after {
    display: none;
  }
}

:deep([class*="-pagination"]) {
  position: static !important;
  font-size: 22px;
  color: #333;
  font-weight: 500;
  width: auto !important;

  .swiper-pagination-current {
    color: rgba(26, 115, 232, 1);
  }

  .swiper-pagination-total {
    color: #999;
  }
}

.slide-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
</style>
