import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import path from "path";
import { AntDesignVueResolver } from "unplugin-vue-components/resolvers";

export default defineNuxtConfig({
  compatibilityDate: "2025-08-08",
  // 文件打包会生成 .map 文件,这里排除掉
  sourcemap: { server: true, client: false },

  // 移除了 naive-ui 模块，antd 不需要特定的 nuxt 模块
  modules: ["@nuxtjs/i18n", "nuxt-swiper"],

  ssr: true, // 启动服务端渲染
  srcDir: "./app", // 配置源码目录在 app 文件夹下,默认 ./ 即业务源码在根路径下

  dir: {
    public: "public", // 指定public目录位置
  },

  alias: {
    "@": "./app",
    "@/axios": "./app/axios/index",
    "@/components": "./app/components/",
    "@/utils": "./app/utils/",
  },

  // 全局样式文件 - 保持使用 less
  css: ["@/styles/style.less", "@/styles/style1.less"],

  experimental: {
    renderJsonPayloads: false,
  },

  app: {
    buildAssetsDir: "/_nuxt/",
    head: {
      title: "CRM",
      meta: [
        { name: "description", content: "CRM" },
        { name: "keyword", content: "CRM" },
        { name: "build_time", content: new Date().toLocaleString() },
        { name: "author", content: "CAY" },
        { name: "copyright", content: "(c) released under the all" },
      ],
      viewport:
        "width=device-width,initial-scale=1,maximum-scale=1,user-scalable=0",
      charset: "utf-8",
      htmlAttrs: {
        lang: "zh-hk",
      },
      link: [{ rel: "icon", type: "image/x-icon", href: "/favicon.ico" }],
    },
  },

  vite: {
    plugins: [
      // SVG配置
      {
        name: "svg-sprite",
        configureServer(server) {
          server.middlewares.use((req, res, next) => {
            if (req.url.endsWith(".svg")) {
              res.setHeader("Content-Type", "image/svg+xml");
            }
            next();
          });
        },
        transform(code, id) {
          if (id.endsWith(".svg")) {
            return {
              code: `export default ${JSON.stringify(code)}`,
              map: null,
            };
          }
        },
      },
      // 配置 ant-design-vue 按需导入
      AutoImport({
        imports: [
          {
            "ant-design-vue": ["message", "notification", "Modal"],
          },
        ],
      }),
      Components({
        // 添加自定义组件的目录
        dirs: ["~/components"],
        // 配置组件的扩展名
        extensions: ["vue"],
        // 配置哪些组件需要自动导入
        include: [/\.vue$/, /\.vue\?vue/],
        resolvers: [
          AntDesignVueResolver({
            importStyle: "less",
            resolveIcons: true,
          }),
        ],
      }),
    ],

    ssr: {
      // 将 ant-design-vue 添加到 noExternal
      noExternal: ["ant-design-vue", "@ant-design/icons-vue"],
    },

    css: {
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
        },
      },
    },

    build: {
      // 更改打包的资源文件名
      rollupOptions: {
        output: {
          chunkFileNames: `_nuxt/js/[hash].js`,
          entryFileNames: `_nuxt/js/[hash].js`,
          assetFileNames: `_nuxt/[ext]/[hash].[ext]`,
        },
      },
    },
  },

  i18n: {
    defaultLocale: "zh-hk",
    detectBrowserLanguage: false,
    locales: [
      { code: "en", name: "English", file: "en.json" },
      { code: "zh-hk", name: "中文繁体", file: "zh-hk.json" },
      { code: "zh-cn", name: "中文简体", file: "zh-cn.json" },
    ],
  },

  swiper: {
    // Swiper 配置选项
    prefix: "Swiper",
    styleLang: "css",
    modules: ["navigation", "pagination", "autoplay"], // 需要的模块
  },

  nitro: {
    preset: "node-server",
    configLock: false,
    output: {
      publicDir: path.resolve(__dirname, "./build/public"),
      dir: path.resolve(__dirname, "./build"),
      serverDir: path.resolve(__dirname, "./build/server"),
    },
  },
});
