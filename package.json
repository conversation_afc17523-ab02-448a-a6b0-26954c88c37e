{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev --host", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@nuxtjs/i18n": "^10.0.3", "ant-design-vue": "^4.2.6", "nuxt": "^4.0.3", "nuxt-swiper": "^2.0.1", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"less": "^4.4.0", "less-loader": "^12.3.0", "svg-sprite-loader": "^6.0.11", "unplugin-auto-import": "^20.0.0", "unplugin-vue-components": "^29.0.0"}}